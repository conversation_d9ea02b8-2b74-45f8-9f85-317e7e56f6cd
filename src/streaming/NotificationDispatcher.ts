import { EventEmitter } from 'events';
import { logger } from '../utils/Logger';
import WebSocket from 'ws';

// ===== NOTIFICATION SYSTEM TYPES =====

export interface NotificationTarget {
  type: 'websocket' | 'sse' | 'repl' | 'sms' | 'slack' | 'teams';
  sessionId: string;
  conversationId?: number;
  connection?: any; // WebSocket, SSE Response, or other connection object
  metadata?: {
    userAgent?: string;
    ip?: string;
    phoneNumber?: string;
    slackChannel?: string;
    teamsChannel?: string;
    [key: string]: any;
  };
}

export interface NotificationMessage {
  id: string;
  type: 'message' | 'typing_start' | 'typing_stop' | 'validation_request' | 'validation_result' | 
        'queue_update' | 'conversation_update' | 'system_notification' | 'chat_complete' | 
        'delayed_thoughts_available' | 'error';
  data: any;
  timestamp: Date;
  priority: number; // 1 = highest, 10 = lowest
  conversationId?: number;
  targetSessions?: string[]; // If specified, only send to these sessions
}

export interface DeliveryResult {
  sessionId: string;
  targetType: string;
  success: boolean;
  error?: string;
}

// ===== NOTIFICATION DISPATCHER =====

export class NotificationDispatcher extends EventEmitter {
  private static instance: NotificationDispatcher;
  
  // Registry of all active clients by session ID
  private clients: Map<string, NotificationTarget[]> = new Map();
  
  // Registry by conversation ID for efficient conversation-wide notifications
  private conversationClients: Map<number, Set<string>> = new Map();
  
  // Message delivery handlers for each client type
  private deliveryHandlers: Map<string, (target: NotificationTarget, message: NotificationMessage) => Promise<boolean>> = new Map();

  private constructor() {
    super();
    this.setupDeliveryHandlers();
  }

  static getInstance(): NotificationDispatcher {
    if (!NotificationDispatcher.instance) {
      NotificationDispatcher.instance = new NotificationDispatcher();
    }
    return NotificationDispatcher.instance;
  }

  // ===== CLIENT REGISTRATION =====

  registerClient(target: NotificationTarget): void {
    const { sessionId, conversationId } = target;
    
    // Add to session registry
    if (!this.clients.has(sessionId)) {
      this.clients.set(sessionId, []);
    }
    
    // Remove any existing target of the same type for this session
    const existingTargets = this.clients.get(sessionId)!;
    const filteredTargets = existingTargets.filter(t => t.type !== target.type);
    filteredTargets.push(target);
    this.clients.set(sessionId, filteredTargets);
    
    // Add to conversation registry if conversation ID is provided
    if (conversationId) {
      if (!this.conversationClients.has(conversationId)) {
        this.conversationClients.set(conversationId, new Set());
      }
      this.conversationClients.get(conversationId)!.add(sessionId);
    }
    
    logger.info(`📡 Registered ${target.type} client for session ${sessionId.substring(0, 8)}... ${conversationId ? `(conversation ${conversationId})` : ''}`);
    
    this.emit('client_registered', { sessionId, type: target.type, conversationId });
  }

  unregisterClient(sessionId: string, targetType?: string): void {
    const targets = this.clients.get(sessionId);
    if (!targets) return;
    
    if (targetType) {
      // Remove specific target type
      const filteredTargets = targets.filter(t => t.type !== targetType);
      if (filteredTargets.length > 0) {
        this.clients.set(sessionId, filteredTargets);
      } else {
        this.clients.delete(sessionId);
      }
      logger.info(`📡 Unregistered ${targetType} client for session ${sessionId.substring(0, 8)}...`);
    } else {
      // Remove all targets for session
      this.clients.delete(sessionId);
      logger.info(`📡 Unregistered all clients for session ${sessionId.substring(0, 8)}...`);
    }
    
    // Clean up conversation registry
    for (const [conversationId, sessionIds] of this.conversationClients.entries()) {
      sessionIds.delete(sessionId);
      if (sessionIds.size === 0) {
        this.conversationClients.delete(conversationId);
      }
    }
    
    this.emit('client_unregistered', { sessionId, type: targetType });
  }

  // ===== NOTIFICATION METHODS =====

  async notifySession(sessionId: string, message: NotificationMessage): Promise<DeliveryResult[]> {
    const targets = this.clients.get(sessionId);
    if (!targets || targets.length === 0) {
      logger.debug(`No clients registered for session ${sessionId.substring(0, 8)}...`);
      return [];
    }

    const results: DeliveryResult[] = [];
    
    for (const target of targets) {
      try {
        const handler = this.deliveryHandlers.get(target.type);
        if (!handler) {
          logger.error(`No delivery handler for client type: ${target.type}`);
          results.push({
            sessionId,
            targetType: target.type,
            success: false,
            error: `No handler for ${target.type}`
          });
          continue;
        }

        const success = await handler(target, message);
        results.push({
          sessionId,
          targetType: target.type,
          success
        });
        
        if (success) {
          logger.debug(`📤 Delivered ${message.type} to ${target.type} client ${sessionId.substring(0, 8)}...`);
        }
      } catch (error) {
        logger.error(`Error delivering message to ${target.type} client ${sessionId.substring(0, 8)}...`, error);
        results.push({
          sessionId,
          targetType: target.type,
          success: false,
          error: (error as Error).message
        });
      }
    }

    this.emit('message_delivered', { sessionId, message, results });
    return results;
  }

  async notifyConversation(conversationId: number, message: NotificationMessage, excludeSession?: string): Promise<DeliveryResult[]> {
    const sessionIds = this.conversationClients.get(conversationId);
    if (!sessionIds || sessionIds.size === 0) {
      logger.debug(`No clients registered for conversation ${conversationId}`);
      return [];
    }

    const allResults: DeliveryResult[] = [];
    
    for (const sessionId of sessionIds) {
      if (excludeSession && sessionId === excludeSession) {
        continue; // Skip excluded session
      }
      
      const results = await this.notifySession(sessionId, message);
      allResults.push(...results);
    }

    logger.info(`📡 Notified ${sessionIds.size} sessions about conversation ${conversationId} update`);
    return allResults;
  }

  async broadcast(message: NotificationMessage): Promise<DeliveryResult[]> {
    const allResults: DeliveryResult[] = [];
    
    for (const sessionId of this.clients.keys()) {
      const results = await this.notifySession(sessionId, message);
      allResults.push(...results);
    }

    logger.info(`📡 Broadcast message to ${this.clients.size} sessions`);
    return allResults;
  }

  // ===== DELIVERY HANDLERS =====

  private setupDeliveryHandlers(): void {
    // WebSocket delivery handler
    this.deliveryHandlers.set('websocket', async (target: NotificationTarget, message: NotificationMessage): Promise<boolean> => {
      const ws = target.connection as WebSocket;
      if (!ws || ws.readyState !== WebSocket.OPEN) {
        return false;
      }

      try {
        ws.send(JSON.stringify({
          id: message.id,
          type: message.type,
          timestamp: message.timestamp,
          ...message.data
        }));
        return true;
      } catch (error) {
        logger.error(`WebSocket send error for session ${target.sessionId}`, error);
        return false;
      }
    });

    // SSE delivery handler
    this.deliveryHandlers.set('sse', async (target: NotificationTarget, message: NotificationMessage): Promise<boolean> => {
      const res = target.connection; // Express Response object
      if (!res || res.destroyed) {
        return false;
      }

      try {
        const sseData = `id: ${message.id}\nevent: ${message.type}\ndata: ${JSON.stringify({
          timestamp: message.timestamp,
          ...message.data
        })}\n\n`;
        
        res.write(sseData);
        return true;
      } catch (error) {
        logger.error(`SSE send error for session ${target.sessionId}`, error);
        return false;
      }
    });

    // REPL delivery handler
    this.deliveryHandlers.set('repl', async (target: NotificationTarget, message: NotificationMessage): Promise<boolean> => {
      // REPL handler will be implemented when we update the REPL interface
      const replHandler = target.connection; // Custom REPL handler
      if (!replHandler || typeof replHandler.handleNotification !== 'function') {
        return false;
      }

      try {
        replHandler.handleNotification(message);
        return true;
      } catch (error) {
        logger.error(`REPL notification error for session ${target.sessionId}`, error);
        return false;
      }
    });

    // SMS delivery handler (placeholder)
    this.deliveryHandlers.set('sms', async (target: NotificationTarget, message: NotificationMessage): Promise<boolean> => {
      // SMS implementation will be added when integrating with SMS interface
      logger.debug(`SMS delivery not yet implemented for session ${target.sessionId}`);
      return false;
    });

    // Slack delivery handler (placeholder)
    this.deliveryHandlers.set('slack', async (target: NotificationTarget, message: NotificationMessage): Promise<boolean> => {
      // Slack implementation will be added when integrating with Slack interface
      logger.debug(`Slack delivery not yet implemented for session ${target.sessionId}`);
      return false;
    });

    // Teams delivery handler (placeholder)
    this.deliveryHandlers.set('teams', async (target: NotificationTarget, message: NotificationMessage): Promise<boolean> => {
      // Teams implementation will be added when integrating with Teams interface
      logger.debug(`Teams delivery not yet implemented for session ${target.sessionId}`);
      return false;
    });
  }

  // ===== UTILITY METHODS =====

  getActiveClients(): { sessionId: string, types: string[], conversationId?: number }[] {
    const result: { sessionId: string, types: string[], conversationId?: number }[] = [];
    
    for (const [sessionId, targets] of this.clients.entries()) {
      result.push({
        sessionId,
        types: targets.map(t => t.type),
        conversationId: targets.find(t => t.conversationId)?.conversationId
      });
    }
    
    return result;
  }

  getConversationClients(conversationId: number): string[] {
    const sessionIds = this.conversationClients.get(conversationId);
    return sessionIds ? Array.from(sessionIds) : [];
  }

  // Generate unique message ID
  generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }
}
